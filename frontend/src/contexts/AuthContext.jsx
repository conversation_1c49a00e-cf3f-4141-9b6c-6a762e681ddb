import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { authAPI, handleAPIError } from '../services/api';

// Initial state
const initialState = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
};

// Action types
const AUTH_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_USER: 'SET_USER',
  SET_ERROR: 'SET_ERROR',
  LOGOUT: 'LOGOUT',
  CLEAR_ERROR: 'CLEAR_ERROR',
  UPDATE_USER: 'UPDATE_USER',
};

// Reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload,
      };

    case AUTH_ACTIONS.SET_USER:
      return {
        ...state,
        user: action.payload,
        isAuthenticated: !!action.payload,
        isLoading: false,
        error: null,
      };

    case AUTH_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };

    case AUTH_ACTIONS.LOGOUT:
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };

    case AUTH_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null,
      };

    case AUTH_ACTIONS.UPDATE_USER:
      return {
        ...state,
        user: {
          ...state.user,
          ...action.payload,
        },
      };

    default:
      return state;
  }
};

// Create context
const AuthContext = createContext();

// Auth provider component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check if user is authenticated on app load
  useEffect(() => {
    checkAuth();
  }, []);

  // Check authentication status
  const checkAuth = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
        return;
      }

      const response = await authAPI.getMe();
      const userData = response.data.user;

      dispatch({ type: AUTH_ACTIONS.SET_USER, payload: userData });
      localStorage.setItem('user', JSON.stringify(userData));
    } catch (error) {
      console.error('Auth check failed:', error);
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
    }
  };

  // Login with token (from OAuth callback)
  const loginWithToken = async (token) => {
    try {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });

      localStorage.setItem('token', token);

      const response = await authAPI.getMe();
      const userData = response.data.user;

      dispatch({ type: AUTH_ACTIONS.SET_USER, payload: userData });
      localStorage.setItem('user', JSON.stringify(userData));

      return { success: true };
    } catch (error) {
      const errorInfo = handleAPIError(error);
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorInfo.message });
      localStorage.removeItem('token');
      return { success: false, error: errorInfo.message };
    }
  };

  // Logout
  const logout = async () => {
    try {
      await authAPI.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
    }
  };

  // Update profile
  const updateProfile = async (profileData) => {
    try {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });

      const response = await authAPI.updateProfile(profileData);
      const updatedUser = response.data.user;

      dispatch({ type: AUTH_ACTIONS.UPDATE_USER, payload: updatedUser });
      localStorage.setItem('user', JSON.stringify({
        ...state.user,
        ...updatedUser,
      }));

      return { success: true };
    } catch (error) {
      const errorInfo = handleAPIError(error);
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorInfo.message });
      return { success: false, error: errorInfo.message };
    } finally {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
    }
  };

  // Switch family
  const switchFamily = async (familyId) => {
    try {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });

      const response = await authAPI.switchFamily(familyId);
      const currentFamily = response.data.currentFamily;

      dispatch({
        type: AUTH_ACTIONS.UPDATE_USER,
        payload: { currentFamily }
      });

      const updatedUser = {
        ...state.user,
        currentFamily,
      };
      localStorage.setItem('user', JSON.stringify(updatedUser));

      return { success: true };
    } catch (error) {
      const errorInfo = handleAPIError(error);
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorInfo.message });
      return { success: false, error: errorInfo.message };
    } finally {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
    }
  };

  // Delete account
  const deleteAccount = async (confirmEmail) => {
    try {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });

      await authAPI.deleteAccount(confirmEmail);

      localStorage.removeItem('token');
      localStorage.removeItem('user');
      dispatch({ type: AUTH_ACTIONS.LOGOUT });

      return { success: true };
    } catch (error) {
      const errorInfo = handleAPIError(error);
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorInfo.message });
      return { success: false, error: errorInfo.message };
    } finally {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
    }
  };

  // Clear error
  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });
  };

  // Local login with credentials
  const loginWithCredentials = async (credentials) => {
    try {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
      dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });

      const response = await authAPI.login(credentials);
      const { token, user } = response.data;

      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(user));

      dispatch({ type: AUTH_ACTIONS.SET_USER, payload: user });

      return { success: true };
    } catch (error) {
      const errorInfo = handleAPIError(error);
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorInfo.message });
      return { success: false, error: errorInfo.message };
    }
  };

  // Google OAuth login
  const loginWithGoogle = () => {
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
    window.location.href = `${apiUrl}/auth/google`;
  };

  // Context value
  const value = {
    ...state,
    loginWithToken,
    loginWithCredentials,
    loginWithGoogle,
    logout,
    updateProfile,
    switchFamily,
    deleteAccount,
    clearError,
    checkAuth,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
