import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { familyAPI, dogAPI, entryAPI } from '../services/api';
import { useApi } from '../hooks/useApi';
import { Plus, Users, Heart, TrendingUp, Calendar } from 'lucide-react';
import LoadingSpinner from '../components/LoadingSpinner';

const DashboardPage = () => {
  const { user } = useAuth();
  const [showCreateEntry, setShowCreateEntry] = useState(false);

  // Fetch families
  const { data: familiesData, loading: familiesLoading, error: familiesError } = useApi(
    familyAPI.getFamilies,
    [],
    { immediate: true }
  );

  // Fetch dogs
  const { data: dogsData, loading: dogsLoading, error: dogsError } = useApi(
    dogAPI.getDogs,
    [],
    { immediate: true }
  );

  // Fetch recent entries
  const { data: entriesData, loading: entriesLoading, error: entriesError } = useApi(
    entryAPI.getEntries,
    [],
    { immediate: true, transform: (data) => data.entries }
  );

  const families = familiesData?.families || [];
  const dogs = dogsData?.dogs || [];
  const recentEntries = entriesData?.slice(0, 5) || [];

  const currentFamily = families.find(f => f._id === user?.currentFamily);
  const hasFamily = families.length > 0;
  const hasDogs = dogs.length > 0;

  if (familiesLoading || dogsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Welcome back, {user?.name?.split(' ')[0]}! 👋
        </h1>
        <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
          {hasFamily && hasDogs
            ? "Here's what's happening with your dogs today."
            : "Let's get you set up to start tracking your dogs!"
          }
        </p>
      </div>

      {/* Quick Stats */}
      {hasFamily && hasDogs && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Users className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Current Family</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {currentFamily?.name || 'No Family'}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Heart className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Dogs</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {dogs.length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Entries</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {recentEntries.length}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Setup Guide */}
      {(!hasFamily || !hasDogs) && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4">
            🚀 Let's get you started!
          </h2>
          <div className="space-y-3">
            {!hasFamily && (
              <div className="flex items-center text-blue-800 dark:text-blue-200">
                <span className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium mr-3">
                  1
                </span>
                <span>Create or join a family to start tracking together</span>
              </div>
            )}
            {hasFamily && !hasDogs && (
              <div className="flex items-center text-blue-800 dark:text-blue-200">
                <span className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium mr-3">
                  {!hasFamily ? '2' : '1'}
                </span>
                <span>Add your dogs to the family</span>
              </div>
            )}
            {hasFamily && hasDogs && (
              <div className="flex items-center text-blue-800 dark:text-blue-200">
                <span className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium mr-3">
                  3
                </span>
                <span>Start tracking your dogs' activities!</span>
              </div>
            )}
          </div>
          <div className="mt-4 flex space-x-3">
            {!hasFamily && (
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                Go to Families
              </button>
            )}
            {hasFamily && !hasDogs && (
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                Add Dogs
              </button>
            )}
          </div>
        </div>
      )}

      {/* Dogs Overview */}
      {hasDogs && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                Your Dogs
              </h2>
              <button className="bg-primary-600 hover:bg-primary-700 text-white px-3 py-1 rounded-md text-sm font-medium transition-colors flex items-center">
                <Plus className="h-4 w-4 mr-1" />
                Add Dog
              </button>
            </div>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {dogs.map((dog) => (
                <div key={dog._id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      {dog.avatar ? (
                        <img
                          src={dog.avatar}
                          alt={dog.name}
                          className="h-12 w-12 rounded-full object-cover"
                        />
                      ) : (
                        <div className="h-12 w-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                          <Heart className="h-6 w-6 text-gray-600 dark:text-gray-400" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {dog.name}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                        {dog.breed} • {dog.age} years old
                      </p>
                    </div>
                  </div>
                  <div className="mt-3">
                    <button className="w-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors">
                      Track Activity
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Recent Entries */}
      {recentEntries.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Recent Activity
            </h2>
          </div>
          <div className="p-6">
            <div className="space-y-3">
              {recentEntries.map((entry) => (
                <div key={entry._id} className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <Calendar className="h-5 w-5 text-gray-400" />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {entry.dog?.name} - {new Date(entry.timestamp).toLocaleDateString()}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Recorded by {entry.recordedBy?.name}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Empty State */}
      {hasFamily && hasDogs && recentEntries.length === 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 text-center">
          <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No entries yet
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Start tracking your dogs' activities to see them here.
          </p>
          <button className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md font-medium transition-colors">
            Create First Entry
          </button>
        </div>
      )}
    </div>
  );
};

export default DashboardPage;
