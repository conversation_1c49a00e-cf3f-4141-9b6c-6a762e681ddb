import React, { useEffect, useState } from 'react';
import { Navigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from '../components/LoadingSpinner';

const AuthCallbackPage = () => {
  const [searchParams] = useSearchParams();
  const { loginWithToken, isAuthenticated } = useAuth();
  const [error, setError] = useState(null);
  const [isProcessing, setIsProcessing] = useState(true);

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const token = searchParams.get('token');
        const errorParam = searchParams.get('error');

        if (errorParam) {
          setError('Authentication failed. Please try again.');
          setIsProcessing(false);
          return;
        }

        if (!token) {
          setError('No authentication token received.');
          setIsProcessing(false);
          return;
        }

        const result = await loginWithToken(token);
        
        if (!result.success) {
          setError(result.error || 'Authentication failed.');
        }
      } catch (err) {
        console.error('Auth callback error:', err);
        setError('An unexpected error occurred during authentication.');
      } finally {
        setIsProcessing(false);
      }
    };

    handleCallback();
  }, [searchParams, loginWithToken]);

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            Authentication Failed
          </h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            {error}
          </p>
          <a href="/login" className="mt-4 inline-block text-primary-600 hover:text-primary-700">
            Try Again
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center">
      <LoadingSpinner size="xl" text="Completing authentication..." />
    </div>
  );
};

export default AuthCallbackPage;
