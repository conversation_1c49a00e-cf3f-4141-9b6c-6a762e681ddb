const mongoose = require('mongoose');
const User = require('../models/User');
const Family = require('../models/Family');
const Dog = require('../models/Dog');
require('dotenv').config({ path: require('path').join(__dirname, '../.env') });

const seedDemoUser = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Check if demo user already exists
    let demoUser = await User.findOne({ username: 'demo' });

    if (!demoUser) {
      // Create demo user
      demoUser = new User({
        username: 'demo',
        password: 'demo123',
        name: 'Demo User',
        email: '<EMAIL>',
        authType: 'local',
        avatar: '',
        preferences: {
          darkMode: false,
          notifications: {
            email: true,
            push: true
          },
          language: 'en'
        }
      });
      await demoUser.save();
      console.log('Demo user created successfully');
    } else {
      console.log('Demo user already exists');
    }

    // Check if demo family already exists
    let demoFamily = await Family.findOne({ name: 'Demo Family' });

    if (!demoFamily) {
      // Create demo family
      demoFamily = new Family({
        name: 'Demo Family',
        description: 'A demo family for testing Pook',
        inviteCode: 'DEMO123',
        members: [{
          user: demoUser._id,
          role: 'admin',
          joinedAt: new Date()
        }],
        createdBy: demoUser._id
      });
      await demoFamily.save();
      console.log('Demo family created successfully');

      // Update user's current family
      demoUser.currentFamily = demoFamily._id;
      demoUser.families = [{
        family: demoFamily._id,
        role: 'admin',
        joinedAt: new Date()
      }];
      await demoUser.save();
      console.log('Demo user updated with family');
    } else {
      console.log('Demo family already exists');
      // Make sure user is part of the family
      if (!demoUser.currentFamily) {
        demoUser.currentFamily = demoFamily._id;
        if (!demoUser.families.some(f => f.family.toString() === demoFamily._id.toString())) {
          demoUser.families.push({
            family: demoFamily._id,
            role: 'admin',
            joinedAt: new Date()
          });
        }
        await demoUser.save();
      }
    }

    // Check if demo dogs already exist
    const existingDogs = await Dog.find({ family: demoFamily._id });

    if (existingDogs.length === 0) {
      // Create demo dogs
      const demoDogs = [
        {
          name: 'Buddy',
          breed: 'Golden Retriever',
          age: 3,
          weight: 30,
          gender: 'male',
          color: 'Golden',
          family: demoFamily._id,
          addedBy: demoUser._id
        },
        {
          name: 'Luna',
          breed: 'Border Collie',
          age: 2,
          weight: 20,
          gender: 'female',
          color: 'Black and White',
          family: demoFamily._id,
          addedBy: demoUser._id
        }
      ];

      for (const dogData of demoDogs) {
        const dog = new Dog(dogData);
        await dog.save();
        console.log(`Demo dog '${dog.name}' created successfully`);
      }
    } else {
      console.log('Demo dogs already exist');
    }

    console.log('\n=== Demo Setup Complete ===');
    console.log('Username: demo');
    console.log('Password: demo123');
    console.log('Family: Demo Family');
    console.log('Dogs: Buddy (Golden Retriever), Luna (Border Collie)');
    console.log('You can now log in and start tracking!');

  } catch (error) {
    console.error('Error seeding demo user:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
};

// Run the seeder
if (require.main === module) {
  seedDemoUser();
}

module.exports = seedDemoUser;
