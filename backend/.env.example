# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
# For MongoDB Atlas (cloud)
MONGODB_URI=mongodb+srv://username:<EMAIL>/pook?retryWrites=true&w=majority
# For local MongoDB (uncomment to use local database)
# MONGODB_URI=mongodb://localhost:27017/pook

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRE=7d

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Session Configuration
SESSION_SECRET=your-session-secret-key

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:3000

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads/

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
