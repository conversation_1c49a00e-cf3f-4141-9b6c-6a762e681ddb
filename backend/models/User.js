const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  googleId: {
    type: String,
    unique: true,
    sparse: true // Allows multiple null values
  },
  username: {
    type: String,
    unique: true,
    sparse: true,
    trim: true
  },
  password: {
    type: String,
    select: false // Don't include password in queries by default
  },
  authType: {
    type: String,
    enum: ['google', 'local'],
    required: true,
    default: 'google'
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  avatar: {
    type: String,
    default: ''
  },
  currentFamily: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Family',
    default: null
  },
  families: [{
    family: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Family'
    },
    role: {
      type: String,
      enum: ['admin', 'member'],
      default: 'member'
    },
    joinedAt: {
      type: Date,
      default: Date.now
    }
  }],
  preferences: {
    darkMode: {
      type: Boolean,
      default: false
    },
    notifications: {
      email: {
        type: Boolean,
        default: true
      },
      push: {
        type: Boolean,
        default: true
      }
    },
    language: {
      type: String,
      default: 'en'
    }
  },
  lastLogin: {
    type: Date,
    default: Date.now
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Index for faster queries
userSchema.index({ googleId: 1 });
userSchema.index({ username: 1 });
userSchema.index({ email: 1 });
userSchema.index({ 'families.family': 1 });

// Virtual for user's role in current family
userSchema.virtual('currentFamilyRole').get(function() {
  if (!this.currentFamily) return null;
  const familyMembership = this.families.find(f =>
    f.family.toString() === this.currentFamily.toString()
  );
  return familyMembership ? familyMembership.role : null;
});

// Method to check if user is admin of a family
userSchema.methods.isAdminOf = function(familyId) {
  const familyMembership = this.families.find(f =>
    f.family.toString() === familyId.toString()
  );
  return familyMembership && familyMembership.role === 'admin';
};

// Method to check if user is member of a family
userSchema.methods.isMemberOf = function(familyId) {
  return this.families.some(f =>
    f.family.toString() === familyId.toString()
  );
};

// Method to get user's families with populated data
userSchema.methods.getPopulatedFamilies = function() {
  return this.populate({
    path: 'families.family',
    select: 'name description dogs members createdAt'
  });
};

// Method to compare password
userSchema.methods.comparePassword = async function(candidatePassword) {
  if (!this.password) return false;
  return await bcrypt.compare(candidatePassword, this.password);
};

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
  // Only hash the password if it has been modified (or is new)
  if (!this.isModified('password')) return next();

  try {
    // Hash password with cost of 12
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

userSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('User', userSchema);
