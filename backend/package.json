{"name": "pook-backend", "version": "1.0.0", "description": "Backend API for Pook - Dog Activity Tracking App", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed:demo": "node scripts/seedDemo.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["dog", "tracking", "family", "webapp"], "author": "<PERSON>", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}